import React, { createContext, useContext, useState, useEffect } from 'react';
import { userSettingsService } from '../services/userSettingsService';
import { localizationService } from '../services/localizationService';
import { formattingService } from '../services/formattingService';
import { supabase } from '../lib/supabase';

const UserPreferencesContext = createContext();

export const useUserPreferences = () => {
  const context = useContext(UserPreferencesContext);
  if (!context) {
    throw new Error('useUserPreferences must be used within a UserPreferencesProvider');
  }
  return context;
};

export const UserPreferencesProvider = ({ children }) => {
  const [preferences, setPreferences] = useState({
    language: 'en',
    timezone: 'America/New_York',
    currency: 'USD',
    loading: true
  });

  // Load user preferences
  const loadPreferences = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const settings = await userSettingsService.getUserSettings(user.id);
        setPreferences({
          language: settings.language || 'en',
          timezone: settings.timezone || 'America/New_York',
          currency: settings.currency || 'USD',
          loading: false
        });
      } else {
        setPreferences(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
      setPreferences(prev => ({ ...prev, loading: false }));
    }
  };

  // Update preferences
  const updatePreferences = async (newPreferences) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const result = await userSettingsService.updateUserPreferences(user.id, newPreferences);
        if (result.success) {
          setPreferences(prev => ({
            ...prev,
            ...newPreferences
          }));
          return { success: true };
        } else {
          throw new Error(result.error);
        }
      }
    } catch (error) {
      console.error('Error updating preferences:', error);
      return { success: false, error: error.message };
    }
  };

  useEffect(() => {
    loadPreferences();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (session?.user) {
        loadPreferences();
      } else {
        setPreferences({
          language: 'en',
          timezone: 'America/New_York',
          currency: 'USD',
          loading: false
        });
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  // Helper functions that use current preferences
  const t = (key) => localizationService.t(key, preferences.language);
  const formatCurrency = (amount, options = {}) =>
    formattingService.formatCurrency(amount, preferences.currency, options);
  const formatCryptoPrice = (price) =>
    formattingService.formatCryptoPrice(price, preferences.currency);
  const formatLargeNumber = (number) =>
    formattingService.formatLargeNumber(number, preferences.currency);
  const formatDate = (date, options = {}) =>
    formattingService.formatDate(date, preferences.timezone, options);
  const formatDisplayDate = (date) =>
    formattingService.formatDisplayDate(date, preferences.timezone);
  const formatPercentage = (value, decimals = 2) =>
    formattingService.formatPercentage(value, decimals);

  const value = {
    preferences,
    updatePreferences,
    loadPreferences,
    // Localization helpers
    t,
    // Formatting helpers
    formatCurrency,
    formatCryptoPrice,
    formatLargeNumber,
    formatDate,
    formatDisplayDate,
    formatPercentage
  };

  return (
    <UserPreferencesContext.Provider value={value}>
      {children}
    </UserPreferencesContext.Provider>
  );
};
