import { supabase } from '../lib/supabase';

class UserSettingsService {
  // Ensure user profile exists, create if it doesn't
  async ensureUserProfile(authId) {
    try {
      // Check if user exists
      const { data: existingUser, error: checkError } = await supabase
        .from('users')
        .select('*')
        .eq('auth_id', authId)
        .single();

      if (existingUser) {
        return { success: true, user: existingUser };
      }

      if (checkError && checkError.code === 'PGRST116') {
        // User doesn't exist, create profile
        console.log('Creating user profile for auth_id:', authId);

        // Get auth user data
        const { data: { user: authUser } } = await supabase.auth.getUser();
        if (!authUser || authUser.id !== authId) {
          throw new Error('Authentication mismatch');
        }

        const referralCode = 'BS' + Math.random().toString(36).substring(2, 10).toUpperCase();

        const newUserData = {
          auth_id: authId,
          name: authUser.user_metadata?.full_name || authUser.email?.split('@')[0] || 'User',
          email: authUser.email,
          first_name: authUser.user_metadata?.first_name || authUser.user_metadata?.full_name?.split(' ')[0] || '',
          last_name: authUser.user_metadata?.last_name || authUser.user_metadata?.full_name?.split(' ')[1] || '',
          balance: 0,
          earned_funds: 0,
          referral_funds: 0,
          referral_code: referralCode,
          avatar_url: null,
          kyc_status: 'pending',
          language: 'en',
          timezone: 'America/New_York',
          currency: 'USD',
          email_notifications: true,
          sms_notifications: false,
          marketing_emails: true,
          login_alerts: true,
          two_factor_enabled: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        const { data: newProfile, error: createError } = await supabase
          .from('users')
          .insert(newUserData)
          .select();

        if (createError) {
          throw createError;
        }

        if (!newProfile || newProfile.length === 0) {
          throw new Error('Failed to create user profile');
        }

        console.log('Created new user profile:', newProfile[0]);
        return { success: true, user: newProfile[0] };
      } else {
        throw checkError;
      }
    } catch (error) {
      console.error('Error ensuring user profile:', error);
      return { success: false, error: error.message };
    }
  }

  // Get user settings
  async getUserSettings(authId) {
    try {
      // Ensure user profile exists
      const profileResult = await this.ensureUserProfile(authId);
      if (!profileResult.success) {
        console.error('Error ensuring user profile:', profileResult.error);
        return this.getDefaultSettings();
      }

      const data = profileResult.user;

      return {
        language: data.language || 'en',
        timezone: data.timezone || 'America/New_York',
        currency: data.currency || 'USD',
        emailNotifications: data.email_notifications !== false, // Default to true
        smsNotifications: data.sms_notifications || false,
        marketingEmails: data.marketing_emails !== false, // Default to true
        loginAlerts: data.login_alerts !== false, // Default to true
        twoFactorEnabled: data.two_factor_enabled || false
      };
    } catch (error) {
      console.error('Error in getUserSettings:', error);
      return this.getDefaultSettings();
    }
  }

  // Get default settings
  getDefaultSettings() {
    return {
      language: 'en',
      timezone: 'America/New_York',
      currency: 'USD',
      emailNotifications: true,
      smsNotifications: false,
      marketingEmails: true,
      loginAlerts: true,
      twoFactorEnabled: false
    };
  }

  // Update user preferences (language, timezone, currency)
  async updateUserPreferences(authId, preferences) {
    try {
      // Ensure user profile exists
      const profileResult = await this.ensureUserProfile(authId);
      if (!profileResult.success) {
        throw new Error(profileResult.error || 'Failed to ensure user profile exists');
      }

      const updates = {};

      if (preferences.language !== undefined) {
        updates.language = preferences.language;
      }
      if (preferences.timezone !== undefined) {
        updates.timezone = preferences.timezone;
      }
      if (preferences.currency !== undefined) {
        updates.currency = preferences.currency;
      }

      updates.updated_at = new Date().toISOString();

      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('auth_id', authId)
        .select();

      if (error) {
        throw error;
      }

      // Check if any rows were updated
      if (!data || data.length === 0) {
        throw new Error('User not found or no changes made');
      }

      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error updating user preferences:', error);
      return { success: false, error: error.message };
    }
  }

  // Update notification settings
  async updateNotificationSettings(authId, notifications) {
    try {
      // Ensure user profile exists
      const profileResult = await this.ensureUserProfile(authId);
      if (!profileResult.success) {
        throw new Error(profileResult.error || 'Failed to ensure user profile exists');
      }

      const updates = {};

      if (notifications.emailNotifications !== undefined) {
        updates.email_notifications = notifications.emailNotifications;
      }
      if (notifications.smsNotifications !== undefined) {
        updates.sms_notifications = notifications.smsNotifications;
      }
      if (notifications.marketingEmails !== undefined) {
        updates.marketing_emails = notifications.marketingEmails;
      }
      if (notifications.loginAlerts !== undefined) {
        updates.login_alerts = notifications.loginAlerts;
      }

      updates.updated_at = new Date().toISOString();

      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('auth_id', authId)
        .select();

      if (error) {
        throw error;
      }

      // Check if any rows were updated
      if (!data || data.length === 0) {
        throw new Error('User not found or no changes made');
      }

      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error updating notification settings:', error);
      return { success: false, error: error.message };
    }
  }

  // Change password using Supabase Auth
  async changePassword(currentPassword, newPassword) {
    try {
      // First verify current password by attempting to sign in
      const { data: user } = await supabase.auth.getUser();
      if (!user?.user?.email) {
        return { success: false, error: 'User not authenticated' };
      }

      // Verify current password
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user.user.email,
        password: currentPassword
      });

      if (signInError) {
        return { success: false, error: 'Current password is incorrect' };
      }

      // Update password
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (updateError) {
        return { success: false, error: updateError.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Error changing password:', error);
      return { success: false, error: error.message };
    }
  }

  // Get user sessions (placeholder for now)
  async getUserSessions(authId) {
    try {
      // For now, return mock session data
      // In a real implementation, you'd track sessions in a separate table
      return [
        {
          id: 'current',
          device: 'Windows • Chrome',
          location: 'New York, NY',
          lastActive: 'Active now',
          isCurrent: true
        }
      ];
    } catch (error) {
      console.error('Error getting user sessions:', error);
      return [];
    }
  }

  // Revoke session (placeholder)
  async revokeSession(sessionId) {
    try {
      // Placeholder implementation
      console.log('Revoking session:', sessionId);
      return { success: true };
    } catch (error) {
      console.error('Error revoking session:', error);
      return { success: false, error: error.message };
    }
  }

  // Enable/disable 2FA (placeholder)
  async toggle2FA(authId, enabled) {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({
          two_factor_enabled: enabled,
          updated_at: new Date().toISOString()
        })
        .eq('auth_id', authId)
        .select();

      if (error) {
        throw error;
      }

      // Check if any rows were updated
      if (!data || data.length === 0) {
        throw new Error('User not found or no changes made');
      }

      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error toggling 2FA:', error);
      return { success: false, error: error.message };
    }
  }

  // Export account data
  async exportAccountData(authId) {
    try {
      // Get user data
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('auth_id', authId)
        .single();

      if (userError) throw userError;

      // Get transactions
      const { data: transactions, error: transError } = await supabase
        .from('transactions')
        .select('*')
        .eq('user_id', authId)
        .order('created_at', { ascending: false });

      if (transError) throw transError;

      // Get investments
      const { data: investments, error: invError } = await supabase
        .from('investments')
        .select('*')
        .eq('user_id', authId)
        .order('created_at', { ascending: false });

      if (invError) throw invError;

      const exportData = {
        user: userData,
        transactions: transactions || [],
        investments: investments || [],
        exportDate: new Date().toISOString()
      };

      // Create downloadable JSON file
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `bullseed-account-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      return { success: true };
    } catch (error) {
      console.error('Error exporting account data:', error);
      return { success: false, error: error.message };
    }
  }

  // Delete account
  async deleteAccount(authId, password) {
    try {
      // Verify password first
      const { data: user } = await supabase.auth.getUser();
      if (!user?.user?.email) {
        return { success: false, error: 'User not authenticated' };
      }

      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user.user.email,
        password: password
      });

      if (signInError) {
        return { success: false, error: 'Password verification failed' };
      }

      // Mark user as deleted (soft delete)
      const { data: updateData, error: updateError } = await supabase
        .from('users')
        .update({
          is_deleted: true,
          deleted_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('auth_id', authId)
        .select();

      if (updateError) {
        throw updateError;
      }

      // Check if any rows were updated
      if (!updateData || updateData.length === 0) {
        throw new Error('User not found or no changes made');
      }

      // Sign out user
      await supabase.auth.signOut();

      return { success: true };
    } catch (error) {
      console.error('Error deleting account:', error);
      return { success: false, error: error.message };
    }
  }

  // Validate password strength
  validatePassword(password) {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    const errors = [];
    
    if (password.length < minLength) {
      errors.push(`Password must be at least ${minLength} characters long`);
    }
    if (!hasUpperCase) {
      errors.push('Password must contain at least one uppercase letter');
    }
    if (!hasLowerCase) {
      errors.push('Password must contain at least one lowercase letter');
    }
    if (!hasNumbers) {
      errors.push('Password must contain at least one number');
    }
    if (!hasSpecialChar) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export default new UserSettingsService();
